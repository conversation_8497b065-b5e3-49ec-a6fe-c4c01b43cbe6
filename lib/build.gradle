plugins {
    id 'com.android.library'
}

android {
    namespace 'com.lody.virtual'
    compileSdk 25

    defaultConfig {
        minSdk 19
        targetSdk 25
        versionCode 1
        versionName "1.0"

        // Temporarily disable NDK build
        // externalNativeBuild {
        //     ndkBuild {
        //         abiFilters "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
        //     }
        // }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // Temporarily disable NDK build for compatibility
    // externalNativeBuild {
    //     ndkBuild {
    //         path file("src/main/jni/Android.mk")
    //     }
    // }

    sourceSets {
        main {
            aidl.srcDirs = ['src/main/aidl']
        }
    }

    lint {
        warning 'NewApi', 'OnClick'
    }

    buildFeatures {
        aidl true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
}
