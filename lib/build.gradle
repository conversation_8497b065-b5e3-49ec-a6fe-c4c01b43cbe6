plugins {
    id 'com.android.library'
}

android {
    namespace 'com.lody.virtual'
    compileSdk 34

    defaultConfig {
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        externalNativeBuild {
            ndkBuild {
                abiFilters "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    externalNativeBuild {
        ndkBuild {
            path file("src/main/jni/Android.mk")
        }
    }

    lint {
        warning 'NewApi', 'OnClick'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
}
