package com.lody.virtual.helper.compat;

import android.os.Build;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * API兼容性工具类
 * 用于处理不同Android版本的API差异和隐藏API访问
 */
public class ApiCompat {
    
    private static final String TAG = "ApiCompat";
    private static final Map<String, Method> sMethodCache = new HashMap<>();
    private static final Map<String, Field> sFieldCache = new HashMap<>();
    private static final Map<String, Class<?>> sClassCache = new HashMap<>();
    
    /**
     * 获取类
     */
    public static Class<?> getClass(String className) {
        Class<?> clazz = sClassCache.get(className);
        if (clazz != null) {
            return clazz;
        }
        
        try {
            clazz = Class.forName(className);
            sClassCache.put(className, clazz);
            return clazz;
        } catch (ClassNotFoundException e) {
            // 尝试从系统类加载器加载
            try {
                clazz = ClassLoader.getSystemClassLoader().loadClass(className);
                sClassCache.put(className, clazz);
                return clazz;
            } catch (ClassNotFoundException e2) {
                return null;
            }
        }
    }
    
    /**
     * 获取方法
     */
    public static Method getMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        if (clazz == null) return null;
        
        String key = clazz.getName() + "#" + methodName;
        Method method = sMethodCache.get(key);
        if (method != null) {
            return method;
        }
        
        try {
            method = clazz.getDeclaredMethod(methodName, parameterTypes);
            method.setAccessible(true);
            sMethodCache.put(key, method);
            return method;
        } catch (NoSuchMethodException e) {
            // 尝试从父类查找
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return getMethod(superClass, methodName, parameterTypes);
            }
            return null;
        }
    }
    
    /**
     * 获取字段
     */
    public static Field getField(Class<?> clazz, String fieldName) {
        if (clazz == null) return null;
        
        String key = clazz.getName() + "#" + fieldName;
        Field field = sFieldCache.get(key);
        if (field != null) {
            return field;
        }
        
        try {
            field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            sFieldCache.put(key, field);
            return field;
        } catch (NoSuchFieldException e) {
            // 尝试从父类查找
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return getField(superClass, fieldName);
            }
            return null;
        }
    }
    
    /**
     * 调用方法
     */
    public static Object invokeMethod(Object obj, Method method, Object... args) {
        if (method == null) return null;
        
        try {
            return method.invoke(obj, args);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取字段值
     */
    public static Object getFieldValue(Object obj, Field field) {
        if (field == null) return null;
        
        try {
            return field.get(obj);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 设置字段值
     */
    public static boolean setFieldValue(Object obj, Field field, Object value) {
        if (field == null) return false;
        
        try {
            field.set(obj, value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查API级别
     */
    public static boolean isAtLeast(int apiLevel) {
        return Build.VERSION.SDK_INT >= apiLevel;
    }
    
    /**
     * 检查是否为特定API级别
     */
    public static boolean isExactly(int apiLevel) {
        return Build.VERSION.SDK_INT == apiLevel;
    }
    
    /**
     * 检查API级别范围
     */
    public static boolean isBetween(int minApi, int maxApi) {
        return Build.VERSION.SDK_INT >= minApi && Build.VERSION.SDK_INT <= maxApi;
    }
}
