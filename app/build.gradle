plugins {
    id 'com.android.application'
    // GreenDAO plugin disabled due to Gradle 8.x compatibility issues
    // id 'org.greenrobot.greendao'
}

android {
    namespace 'com.wstro.virtuallocation'
    compileSdk 25

    defaultConfig {
        applicationId "com.wstro.virtuallocation"
        minSdk 19
        targetSdk 25
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        multiDexEnabled true
        ndk {
            abiFilters "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    lint {
        checkReleaseBuilds false
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }
}

// GreenDAO configuration disabled due to plugin compatibility issues
// greendao {
//     schemaVersion 1
//     daoPackage "com.wstro.virtuallocation.data.db"
//     targetGenDir "src/main/java"
// }

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':lib')

    // AndroidX dependencies (compatible with compileSdk 25)
    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.multidex:multidex:2.0.1'

    // Force AndroidX versions to avoid conflicts
    configurations.all {
        resolutionStrategy {
            force 'androidx.core:core:1.9.0'
            force 'androidx.appcompat:appcompat:1.6.1'
            force 'androidx.fragment:fragment:1.5.5'
            force 'androidx.activity:activity:1.6.1'
        }
    }

    // Testing dependencies
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    // Third-party libraries (compatible with compileSdk 25)
    implementation 'com.wang.avi:library:2.1.3'
    implementation 'com.classic.common:multiple-status-view:1.3'
    implementation('com.github.pengliangAndroid:appframework:1.7') {
        exclude group: 'com.google.code.findbugs'
    }
    implementation 'org.greenrobot:greendao:3.1.1'
    implementation 'com.github.zcweng:switch-button:0.0.3@aar'

    // Local JAR files
    implementation files('libs/BaiduLBS_Android.jar')

    // Utility libraries
    implementation 'com.belerweb:pinyin4j:2.5.1'
    implementation 'com.oushangfeng:PinnedSectionItemDecoration:1.2.6'

    // ButterKnife for backward compatibility (to be migrated to View Binding)
    implementation 'com.jakewharton:butterknife:10.2.3'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'
}
